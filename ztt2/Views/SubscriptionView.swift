//
//  SubscriptionView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 订阅页面主视图
 * 基于ztt1项目的订阅页面实现，完全复用UI设计和交互逻辑
 * 支持初级会员和高级会员的切换显示，包含用户信息、分段选项卡、权益列表、价格卡片等
 */
struct SubscriptionView: View {

    // MARK: - Properties
    let onDismiss: () -> Void

    // MARK: - State
    @State private var selectedMembershipType: Int = 1 // 0: 初级会员, 1: 高级会员，默认显示高级会员
    @State private var selectedPriceType: Int = 0 // 0: 月会员, 1: 年会员
    @State private var agreementAccepted: Bool = false
    @State private var backgroundAppeared = false
    @State private var contentAppeared = false
    @State private var tabAppeared = false

    // MARK: - Computed Properties

    // 模拟用户数据 - 在实际应用中应该从用户管理系统获取
    private var userName: String {
        return "user_info.parent_nickname".localized
    }

    private var userID: String {
        return "123456" // 模拟用户ID
    }

    private var membershipLevel: String {
        return "subscription.user_level_regular".localized
    }

    private var expirationDate: String {
        return "subscription.not_activated".localized
    }

    var body: some View {
        GeometryReader { geometry in
            setupGeometry(geometry: geometry)
        }
        .onAppear {
            setupEntranceAnimations()
        }
    }

    // MARK: - Private Methods

    /**
     * 设置几何布局
     */
    private func setupGeometry(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height

        return ZStack {
            // 第零层：装饰性背景系统
            createDecorativeBackground(geometry: geometry)

            // 第一层：独立组件层
            ZStack {
                // 个人信息组件 - 绝对定位到顶部30%
                SubscriptionUserInfoSection(
                    userName: userName,
                    userID: userID,
                    membershipLevel: membershipLevel,
                    expirationDate: expirationDate,
                    onBackPressed: handleBackPressed
                )
                .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.heightPercentage)
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.topPositionPercentage
                )
                .zIndex(3) // 确保在背景图之上

                // 会员内容组件 - 绝对定位到下方
                MembershipContentView(
                    selectedMembershipType: $selectedMembershipType,
                    selectedPriceType: $selectedPriceType,
                    agreementAccepted: $agreementAccepted,
                    isLoading: false, // 简化版本，不包含购买逻辑
                    purchaseSuccess: false,
                    errorMessage: nil,
                    onSubscribePressed: handleSubscribePressed
                )
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.ContentSection.topOffsetPercentage + 120
                )
                .zIndex(3) // 确保在背景图之上
            }

            // 第三层：分段选项卡层（最上层，透明背景）
            VStack {
                Spacer()
                    .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.topOffsetPercentage)

                MembershipTabSegment(
                    selectedTab: $selectedMembershipType,
                    onTabChanged: handleTabChanged
                )
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight + 20)

                Spacer()
            }
            .zIndex(4) // 最高层级，确保可交互
        }
        .frame(width: screenWidth, height: screenHeight)
        .clipped()
    }

    /**
     * 设置入场动画
     */
    private func setupEntranceAnimations() {
        // 背景动画
        withAnimation(.easeIn(duration: DesignSystem.SubscriptionPage.AnimationPresets.Background.fadeInDuration)) {
            backgroundAppeared = true
        }

        // 内容动画
        withAnimation(.easeOut(duration: DesignSystem.SubscriptionPage.AnimationPresets.Content.duration).delay(DesignSystem.SubscriptionPage.AnimationPresets.Content.delay)) {
            contentAppeared = true
        }

        // 选项卡动画
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(DesignSystem.SubscriptionPage.AnimationPresets.Tab.delay)) {
            tabAppeared = true
        }
    }

    /**
     * 创建装饰性背景系统
     * 参考个人中心页面的美化背景设计
     */
    private func createDecorativeBackground(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height

        return ZStack {
            // 美化背景渐变 - 与个人中心页面保持一致的风格
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            .opacity(backgroundAppeared ? 1.0 : 0.0)
            .animation(.easeIn(duration: DesignSystem.SubscriptionPage.AnimationPresets.Background.fadeInDuration), value: backgroundAppeared)

            // 装饰性几何图形
            createDecorativeShapes(screenWidth: screenWidth, screenHeight: screenHeight)
        }
        .zIndex(0) // 最底层
    }

    /**
     * 创建装饰性几何图形
     */
    private func createDecorativeShapes(screenWidth: CGFloat, screenHeight: CGFloat) -> some View {
        ZStack {
            // 左上角大圆圈
            Circle()
                .fill(Color(hex: "#e7f7c4").opacity(0.3))
                .frame(width: 200, height: 200)
                .position(x: screenWidth * 0.1, y: screenHeight * 0.15)
                .opacity(backgroundAppeared ? 1.0 : 0.0)
                .scaleEffect(backgroundAppeared ? 1.0 : 0.5)
                .animation(.spring(response: 1.2, dampingFraction: 0.8).delay(0.3), value: backgroundAppeared)

            // 右下角中圆圈
            Circle()
                .fill(Color(hex: "#a9cd53").opacity(0.2))
                .frame(width: 150, height: 150)
                .position(x: screenWidth * 0.9, y: screenHeight * 0.85)
                .opacity(backgroundAppeared ? 1.0 : 0.0)
                .scaleEffect(backgroundAppeared ? 1.0 : 0.3)
                .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.5), value: backgroundAppeared)

            // 中间小圆圈
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.4))
                .frame(width: 80, height: 80)
                .position(x: screenWidth * 0.8, y: screenHeight * 0.3)
                .opacity(backgroundAppeared ? 1.0 : 0.0)
                .scaleEffect(backgroundAppeared ? 1.0 : 0.2)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.7), value: backgroundAppeared)
        }
    }

    /**
     * 处理返回按钮点击
     */
    private func handleBackPressed() {
        print("🔙 返回按钮被点击")
        onDismiss()
    }

    /**
     * 处理分段选项卡切换
     */
    private func handleTabChanged(to type: Int) {
        let membershipTypeName = type == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        print("会员类型切换到: \(membershipTypeName)")

        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            // selectedMembershipType 通过 Binding 自动更新
            // 重置价格选择为月会员
            selectedPriceType = 0
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理订阅按钮点击
     */
    private func handleSubscribePressed() {
        let membershipTypeName = selectedMembershipType == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        let priceTypeName = selectedPriceType == 0 ? "subscription.pricing.monthly".localized : "subscription.pricing.yearly".localized

        print("🛒 订阅按钮被点击 - \(membershipTypeName) \(priceTypeName)")

        // 简化版本：只显示提示信息
        print("📝 注意：这是演示版本，实际购买功能需要集成RevenueCat等支付系统")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 可以在这里添加实际的购买逻辑
        // 例如：调用RevenueCat购买API、显示支付界面等
    }
}

// MARK: - Preview
#Preview {
    SubscriptionView {
        print("关闭订阅页面")
    }
}
