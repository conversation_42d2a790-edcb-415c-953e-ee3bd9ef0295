//
//  BackgroundImageLayer.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 背景图层管理组件
 * 负责管理初级会员和高级会员背景图的切换显示
 * 横向填满整个屏幕，位于分段选项卡下方
 * 支持用户手动调整背景图的大小、位置和特效
 */
struct BackgroundImageLayer: View {
    
    // MARK: - Properties
    @Binding var selectedMembershipType: Int // 0: 初级会员, 1: 高级会员
    
    // MARK: - State
    @State private var backgroundOpacity: [Double] = [0.0, 1.0] // [初级, 高级]
    @State private var layerAppeared = false
    @State private var backgroundScale: CGFloat = 1.1
    @State private var gradientOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // 初级会员背景图
            createBackgroundImage(
                imageName: DesignSystem.SubscriptionPage.BackgroundImage.primaryImageName,
                opacity: backgroundOpacity[0],
                isSelected: selectedMembershipType == 0
            )
            
            // 高级会员背景图
            createBackgroundImage(
                imageName: DesignSystem.SubscriptionPage.BackgroundImage.advancedImageName,
                opacity: backgroundOpacity[1],
                isSelected: selectedMembershipType == 1
            )
            
            // 渐变叠加层 - 增强视觉深度
            createGradientOverlay()
        }
        .ignoresSafeArea(.all) // 横向填满整个屏幕
        .allowsHitTesting(false) // 不拦截触摸事件，让底层组件可交互
        .opacity(layerAppeared ? 1.0 : 0.0)
        .animation(.easeIn(duration: 0.6).delay(0.2), value: layerAppeared)
        .onChange(of: selectedMembershipType) { newValue in
            switchBackgroundImage(to: newValue)
        }
        .onAppear {
            setupInitialState()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        withAnimation {
            layerAppeared = true
        }
        
        // 根据当前选择设置背景透明度
        if selectedMembershipType == 0 {
            backgroundOpacity = [1.0, 0.0]
        } else {
            backgroundOpacity = [0.0, 1.0]
        }
        
        // 启动渐变动画
        withAnimation(.easeInOut(duration: 1.0).delay(0.5)) {
            gradientOpacity = 0.3
        }
    }
    
    /**
     * 创建背景图
     */
    private func createBackgroundImage(imageName: String, opacity: Double, isSelected: Bool) -> some View {
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.self
        
        return GeometryReader { geometry in
            Image(imageName)
                .resizable()
                .aspectRatio(contentMode: getContentMode())
                // 应用尺寸缩放 - 结合动态缩放效果
                .scaleEffect(
                    x: config.scaleX * config.uniformScale * (isSelected ? backgroundScale : backgroundScale * 0.95),
                    y: config.scaleY * config.uniformScale * (isSelected ? backgroundScale : backgroundScale * 0.95)
                )
                // 应用位置偏移
                .offset(
                    x: config.offsetX,
                    y: config.offsetY
                )
                // 应用裁剪 - 使用frame来实现裁剪效果
                .frame(
                    width: geometry.size.width * (1 - config.cropLeft - config.cropRight),
                    height: geometry.size.height * (1 - config.cropTop - config.cropBottom)
                )
                .clipped()
                // 应用特效
                .blur(radius: config.blur)
                .brightness(config.brightness)
                .contrast(config.contrast)
                // 应用透明度
                .opacity(opacity * config.opacity)
                // 应用动画
                .animation(
                    .easeInOut(duration: config.animationDuration),
                    value: opacity
                )
        }
        // 应用层级
        .zIndex(config.zIndex)
    }
    
    /**
     * 根据对齐模式获取内容模式
     */
    private func getContentMode() -> ContentMode {
        switch DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.alignmentMode {
        case .fill:
            return .fill
        case .fit:
            return .fit
        case .stretch:
            return .fill // SwiftUI中没有直接的stretch模式，使用fill代替
        }
    }
    
    /**
     * 切换背景图
     * - Parameter type: 会员类型 (0: 初级, 1: 高级)
     */
    private func switchBackgroundImage(to type: Int) {
        withAnimation(.easeInOut(duration: DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.animationDuration)) {
            if type == 0 {
                // 显示初级会员背景
                backgroundOpacity = [1.0, 0.0]
            } else {
                // 显示高级会员背景
                backgroundOpacity = [0.0, 1.0]
            }
        }
        
        // 切换时重置缩放动画
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            backgroundScale = 1.05
        }
        
        // 恢复缩放
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.2)) {
            backgroundScale = 1.0
        }
    }
    
    /**
     * 创建渐变叠加层
     */
    private func createGradientOverlay() -> some View {
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0.0),
                .init(color: Color.white.opacity(0.1), location: 0.3),
                .init(color: Color.clear, location: 0.7),
                .init(color: Color.white.opacity(0.05), location: 1.0)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .opacity(gradientOpacity)
        .animation(.easeInOut(duration: 2.0), value: gradientOpacity)
        .allowsHitTesting(false)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color(hex: "#f8fdf0")
            .ignoresSafeArea()
        
        BackgroundImageLayer(selectedMembershipType: .constant(0))
        
        VStack {
            Text("背景图层预览")
                .font(.title)
                .foregroundColor(.black)
            
            Spacer()
        }
        .padding()
    }
}
