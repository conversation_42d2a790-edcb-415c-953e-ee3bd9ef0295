//
//  SubscriptionUserInfoSection.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 订阅页面个人信息组件
 * 重构版本：所有元素使用绝对定位，互不影响
 * 每个元素都有独立的坐标系统
 */
struct SubscriptionUserInfoSection: View {
    
    // MARK: - Properties
    let userName: String
    let userID: String
    let membershipLevel: String
    let expirationDate: String
    let onBackPressed: () -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    @State private var crownRotation: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            setupAbsoluteLayout(geometry: geometry)
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置绝对定位布局
     * 每个元素都有独立的坐标系统，可以单独调整位置而不影响其他元素
     */
    private func setupAbsoluteLayout(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height
        
        return ZStack {
            // 背景容器 - 占满整个区域
            createBackgroundContainer()
                .frame(width: screenWidth, height: screenHeight)
                .position(x: screenWidth / 2, y: screenHeight / 2)
            
            // 装饰性背景元素 - 绝对定位
            createDecorationCircles(screenWidth: screenWidth, screenHeight: screenHeight)
            
            // 返回按钮 - 绝对定位，独立坐标系
            createBackButton()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.backButtonPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.backButtonPositionY
                )
            
            // 用户头像 - 绝对定位，独立坐标系
            createUserAvatar()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.avatarPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.avatarPositionY
                )
            
            // 用户信息文本 - 绝对定位，独立坐标系
            createUserInfoText()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.userInfoPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.userInfoPositionY
                )
            
            // 皇冠图标 - 绝对定位，独立坐标系
            createCrownIcon()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.crownPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.crownPositionY
                )
        }
    }
    
    /**
     * 创建背景容器
     */
    private func createBackgroundContainer() -> some View {
        Rectangle()
            .fill(Color.clear) // 透明背景，让下层的装饰背景透过
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .animation(.easeIn(duration: 0.6), value: sectionAppeared)
    }
    
    /**
     * 创建装饰性背景圆圈
     */
    private func createDecorationCircles(screenWidth: CGFloat, screenHeight: CGFloat) -> some View {
        ZStack {
            // 左上角装饰圆圈
            Circle()
                .fill(Color(hex: "#e7f7c4").opacity(0.3))
                .frame(width: 120, height: 120)
                .position(x: screenWidth * 0.15, y: screenHeight * 0.2)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.5)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: sectionAppeared)
            
            // 右下角装饰圆圈
            Circle()
                .fill(Color(hex: "#a9cd53").opacity(0.2))
                .frame(width: 80, height: 80)
                .position(x: screenWidth * 0.85, y: screenHeight * 0.8)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.3)
                .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.4), value: sectionAppeared)
        }
    }
    
    /**
     * 创建返回按钮
     */
    private func createBackButton() -> some View {
        Button(action: onBackPressed) {
            Image(systemName: "chevron.left")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(Color(hex: "#666666"))
                .frame(width: 40, height: 40)
                .background(Color.white.opacity(0.8))
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .offset(x: sectionAppeared ? 0 : -30)
        .animation(.easeOut(duration: 0.6).delay(0.1), value: sectionAppeared)
    }
    
    /**
     * 创建用户头像
     */
    private func createUserAvatar() -> some View {
        Circle()
            .fill(Color(hex: "#a9cd53"))
            .frame(
                width: DesignSystem.SubscriptionPage.UserInfoSection.avatarSize,
                height: DesignSystem.SubscriptionPage.UserInfoSection.avatarSize
            )
            .overlay(
                Image(systemName: "person.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
            )
            .shadow(color: Color(hex: "#a9cd53").opacity(0.3), radius: 8, x: 0, y: 4)
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .offset(x: sectionAppeared ? 0 : -20, y: 0)
            .scaleEffect(sectionAppeared ? 1.0 : 0.8)
            .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.2), value: sectionAppeared)
    }
    
    /**
     * 创建用户信息文本
     */
    private func createUserInfoText() -> some View {
        VStack(alignment: .leading, spacing: 4) {
            // 用户名称
            Text(userName)
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.nameFont, weight: .semibold))
                .foregroundColor(Color(hex: "#333333"))
            
            // 会员等级
            Text(membershipLevel)
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.membershipFont, weight: .regular))
                .foregroundColor(Color(hex: "#a9cd53"))
            
            // 用户ID - 与个人中心保持一致的显示格式
            Text("profile.user_info.id_label".localized(with: userID))
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.idFont, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            // 会员到期时间 - 与个人中心保持一致的显示格式
            Text("profile.user_info.membership_expires".localized(with: expirationDate))
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.dateFont, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .offset(x: sectionAppeared ? 0 : -20, y: 0)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: sectionAppeared)
    }
    
    /**
     * 创建皇冠图标
     */
    private func createCrownIcon() -> some View {
        ZStack {
            // 皇冠背景光晕
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.2))
                .frame(
                    width: DesignSystem.SubscriptionPage.UserInfoSection.crownSize + 20,
                    height: DesignSystem.SubscriptionPage.UserInfoSection.crownSize + 20
                )
                .blur(radius: 10)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.5)
                .animation(.easeOut(duration: 1.0).delay(0.5), value: sectionAppeared)
            
            // 主皇冠图标
            Image("皇冠(订阅）")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(
                    width: DesignSystem.SubscriptionPage.UserInfoSection.crownSize,
                    height: DesignSystem.SubscriptionPage.UserInfoSection.crownSize
                )
                .rotationEffect(.degrees(DesignSystem.SubscriptionPage.UserInfoSection.crownRotation + crownRotation))
                .shadow(color: Color(hex: "#FFE49E").opacity(0.4), radius: 8, x: 0, y: 0)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(x: sectionAppeared ? 0 : 30)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.4), value: sectionAppeared)
            
            // 闪烁星星效果
            ForEach(0..<6, id: \.self) { index in
                createSparkleEffect(index: index)
            }
        }
        .onAppear {
            // 启动皇冠旋转动画
            withAnimation(.linear(duration: 20).repeatForever(autoreverses: false)) {
                crownRotation = 360
            }
        }
    }
    
    /**
     * 创建闪烁星星效果
     */
    private func createSparkleEffect(index: Int) -> some View {
        let angle = Double(index) * 60.0 // 每60度一个星星
        let radius: CGFloat = 60
        let x = cos(angle * .pi / 180) * radius
        let y = sin(angle * .pi / 180) * radius
        
        return Image(systemName: "star.fill")
            .font(.system(size: 8))
            .foregroundColor(Color(hex: "#FFE49E"))
            .offset(x: x, y: y)
            .opacity(sectionAppeared ? 0.8 : 0.0)
            .scaleEffect(sectionAppeared ? 1.0 : 0.3)
            .animation(
                .easeInOut(duration: 1.5)
                .repeatForever(autoreverses: true)
                .delay(Double(index) * 0.2 + 0.6),
                value: sectionAppeared
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color(hex: "#f8fdf0")
            .ignoresSafeArea()
        
        SubscriptionUserInfoSection(
            userName: "user_info.parent_nickname".localized,
            userID: "123456",
            membershipLevel: "subscription.user_level_regular".localized,
            expirationDate: "未开通"
        ) {
            print("返回按钮被点击")
        }
        .frame(height: 300)
    }
}
