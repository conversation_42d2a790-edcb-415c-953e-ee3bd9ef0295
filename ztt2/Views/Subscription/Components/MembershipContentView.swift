//
//  MembershipContentView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 会员内容展示组件
 * 包含权益描述、价格标签、订阅按钮和服务协议勾选
 */
struct MembershipContentView: View {
    
    // MARK: - Properties
    @Binding var selectedMembershipType: Int // 0: 初级, 1: 高级
    @Binding var selectedPriceType: Int // 0: 月会员, 1: 年会员
    @Binding var agreementAccepted: Bool
    let isLoading: Bool
    let purchaseSuccess: Bool
    let errorMessage: String?
    let onSubscribePressed: () -> Void
    
    // MARK: - Computed Properties
    private var primaryFeatures: [String] {
        return [
            "subscription.feature.manage_classes_two".localized,
            "subscription.feature.unlock_wheel".localized,
            "subscription.feature.multi_device_sync".localized,
            "subscription.feature.basic_functions".localized
        ]
    }
    
    private var advancedFeatures: [String] {
        return [
            "subscription.feature.all_basic_benefits".localized,
            "subscription.feature.manage_classes_five".localized,
            "subscription.feature.unlock_box_scratch".localized,
            "subscription.feature.ai_reports".localized
        ]
    }
    
    private var currentFeatures: [String] {
        return selectedMembershipType == 0 ? primaryFeatures : advancedFeatures
    }
    
    // 价格数据
    private var priceData: [(title: String, price: String, unit: String)] {
        return [
            (title: "subscription.pricing.monthly".localized, price: "38", unit: "元"),
            (title: "subscription.pricing.yearly".localized, price: "188", unit: "元")
        ]
    }
    
    private var advancedPriceData: [(title: String, price: String, unit: String)] {
        return [
            (title: "subscription.pricing.monthly".localized, price: "58", unit: "元"),
            (title: "subscription.pricing.yearly".localized, price: "288", unit: "元")
        ]
    }
    
    // MARK: - State
    @State private var contentAppeared = false
    @State private var subscribeButtonPressed = false
    @State private var showAgreementReminder = false
    
    var body: some View {
        VStack(spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.ContentSection.verticalSpacing) {
            // 权益列表
            createFeatureList()
            
            // 价格卡片
            createPriceCards()
            
            // 订阅按钮
            createSubscribeButton()
            
            // 协议勾选
            createAgreementSection()
        }
        .padding(.horizontal, DesignSystem.AdaptiveLayout.SubscriptionPage.ContentSection.horizontalPadding)
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : DesignSystem.SubscriptionPage.AnimationPresets.Content.offsetY)
        .animation(
            .easeOut(duration: DesignSystem.SubscriptionPage.AnimationPresets.Content.duration)
            .delay(DesignSystem.SubscriptionPage.AnimationPresets.Content.delay),
            value: contentAppeared
        )
        .onAppear {
            withAnimation {
                contentAppeared = true
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建权益列表
     */
    private func createFeatureList() -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.FeatureList.itemSpacing) {
            ForEach(Array(currentFeatures.enumerated()), id: \.offset) { index, feature in
                createFeatureItem(feature: feature, index: index)
            }
        }
        .animation(.easeInOut(duration: 0.5), value: selectedMembershipType)
    }
    
    /**
     * 创建单个权益项
     */
    private func createFeatureItem(feature: String, index: Int) -> some View {
        HStack(spacing: 12) {
            // 皇冠图标
            Image("皇冠(订阅）")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(
                    width: DesignSystem.AdaptiveLayout.SubscriptionPage.FeatureList.iconSize,
                    height: DesignSystem.AdaptiveLayout.SubscriptionPage.FeatureList.iconSize
                )
            
            // 权益文本
            Text(feature)
                .font(.system(
                    size: DesignSystem.AdaptiveLayout.SubscriptionPage.FeatureList.fontSize,
                    weight: DesignSystem.SubscriptionPage.FeatureList.fontWeight
                ))
                .foregroundColor(DesignSystem.SubscriptionPage.FeatureList.textColor)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(x: contentAppeared ? 0 : DesignSystem.SubscriptionPage.AnimationPresets.FeatureList.offsetX)
        .animation(
            .easeOut(duration: DesignSystem.SubscriptionPage.AnimationPresets.FeatureList.baseDuration)
            .delay(DesignSystem.SubscriptionPage.AnimationPresets.Content.delay + Double(index) * DesignSystem.SubscriptionPage.AnimationPresets.FeatureList.itemDelay),
            value: contentAppeared
        )
    }
    
    /**
     * 创建价格卡片
     * 修复版本：使用稳定的ID避免切换时的跳动动画
     */
    private func createPriceCards() -> some View {
        let prices = selectedMembershipType == 0 ? priceData : advancedPriceData
        
        return HStack(spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.spacing) {
            // 月会员卡片 - 固定ID
            createPriceCard(
                title: prices[0].title,
                price: prices[0].price,
                unit: prices[0].unit,
                isSelected: selectedPriceType == 0,
                index: 0
            )
            .id("monthly_card") // 固定ID，避免重建
            
            // 年会员卡片 - 固定ID
            createPriceCard(
                title: prices[1].title,
                price: prices[1].price,
                unit: prices[1].unit,
                isSelected: selectedPriceType == 1,
                index: 1
            )
            .id("yearly_card") // 固定ID，避免重建
        }
        .animation(.easeInOut(duration: 0.25), value: selectedMembershipType) // 会员类型切换时的整体平滑动画
    }
    
    /**
     * 创建单个价格卡片
     */
    private func createPriceCard(title: String, price: String, unit: String, isSelected: Bool, index: Int) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                selectedPriceType = index
            }
            
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            VStack(spacing: 8) {
                // 标题
                Text(title)
                    .font(.system(
                        size: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.titleFont,
                        weight: .medium
                    ))
                    .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.titleColor)
                
                // 价格
                HStack(alignment: .bottom, spacing: 4) {
                    Text("¥")
                        .font(.system(
                            size: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.unitFont,
                            weight: .semibold
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.priceColor)
                    
                    Text(price)
                        .font(.system(
                            size: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.priceFont,
                            weight: .bold
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.priceColor)
                }
            }
            .frame(
                width: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.width,
                height: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.height
            )
            .background(
                isSelected ? 
                DesignSystem.SubscriptionPage.PriceCard.selectedBackgroundColor :
                DesignSystem.SubscriptionPage.PriceCard.unselectedBackgroundColor
            )
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.cornerRadius)
                    .stroke(
                        isSelected ? 
                        DesignSystem.SubscriptionPage.PriceCard.selectedBorderColor :
                        DesignSystem.SubscriptionPage.PriceCard.unselectedBorderColor,
                        lineWidth: isSelected ? 
                        DesignSystem.SubscriptionPage.PriceCard.selectedBorderWidth :
                        DesignSystem.SubscriptionPage.PriceCard.unselectedBorderWidth
                    )
            )
            .cornerRadius(DesignSystem.AdaptiveLayout.SubscriptionPage.PriceCard.cornerRadius)
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .shadow(
                color: isSelected ? 
                DesignSystem.SubscriptionPage.PriceCard.selectedBorderColor.opacity(0.3) :
                Color.clear,
                radius: isSelected ? 8 : 0,
                x: 0,
                y: isSelected ? 4 : 0
            )
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: price) // 添加价格变化的平滑动画
    }

    /**
     * 创建订阅按钮
     */
    private func createSubscribeButton() -> some View {
        VStack(spacing: 8) {
            // 订阅按钮
            Button(action: {
                print("🔘 订阅按钮被点击")
                print("🔍 协议接受状态: \(agreementAccepted)")

                guard agreementAccepted else {
                    print("⚠️ 用户未勾选协议，阻止购买")
                    // 显示协议提醒
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        showAgreementReminder = true
                    }

                    // 3秒后自动隐藏提醒
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            showAgreementReminder = false
                        }
                    }

                    // 触觉反馈 - 错误
                    let feedbackGenerator = UINotificationFeedbackGenerator()
                    feedbackGenerator.notificationOccurred(.error)
                    return
                }

                guard !isLoading else {
                    return // 正在加载中，禁止重复点击
                }

                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    subscribeButtonPressed = true
                }

                onSubscribePressed()

                // 恢复按钮状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    subscribeButtonPressed = false
                }

                // 触觉反馈 - 成功
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }) {
                HStack(spacing: 8) {
                    // 加载指示器
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }

                    // 按钮文本
                    Text(getButtonText())
                        .font(.system(
                            size: DesignSystem.AdaptiveLayout.SubscriptionPage.SubscribeButton.fontSize,
                            weight: DesignSystem.SubscriptionPage.SubscribeButton.fontWeight
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.SubscribeButton.textColor)
                }
                .frame(maxWidth: DesignSystem.AdaptiveLayout.SubscriptionPage.SubscribeButton.maxWidth)
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.SubscribeButton.height)
                .background(
                    isLoading || !agreementAccepted ?
                    DesignSystem.SubscriptionPage.SubscribeButton.disabledBackgroundColor :
                    DesignSystem.SubscriptionPage.SubscribeButton.backgroundColor
                )
                .cornerRadius(DesignSystem.AdaptiveLayout.SubscriptionPage.SubscribeButton.cornerRadius)
                .shadow(
                    color: (isLoading || !agreementAccepted) ? Color.clear : DesignSystem.SubscriptionPage.SubscribeButton.shadowColor,
                    radius: DesignSystem.SubscriptionPage.SubscribeButton.shadowRadius,
                    x: 0,
                    y: DesignSystem.SubscriptionPage.SubscribeButton.shadowOffsetY
                )
                .scaleEffect(subscribeButtonPressed ? DesignSystem.SubscriptionPage.AnimationPresets.SubscribeButton.pressedScale : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: subscribeButtonPressed)
                .animation(.easeInOut(duration: 0.3), value: isLoading)
                .animation(.easeInOut(duration: 0.3), value: agreementAccepted)
            }
            .disabled(isLoading || !agreementAccepted)

            // 协议提醒文本
            if showAgreementReminder {
                Text("purchase.agreement.reminder".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.errorColor)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(DesignSystem.Colors.errorColor.opacity(0.1))
                    .cornerRadius(8)
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .scaleEffect(
            contentAppeared ?
            DesignSystem.SubscriptionPage.AnimationPresets.SubscribeButton.scaleStart :
            DesignSystem.SubscriptionPage.AnimationPresets.SubscribeButton.scaleStart
        )
        .animation(
            .spring(response: 0.6, dampingFraction: 0.8)
            .delay(DesignSystem.SubscriptionPage.AnimationPresets.SubscribeButton.delay),
            value: contentAppeared
        )
    }

    /**
     * 创建协议勾选区域
     */
    private func createAgreementSection() -> some View {
        HStack(spacing: 8) {
            // 勾选框
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    agreementAccepted.toggle()
                }

                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }) {
                Image(systemName: agreementAccepted ? "checkmark.square.fill" : "square")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(
                        agreementAccepted ?
                        Color(hex: "#a9cd53") :
                        Color(hex: "#cccccc")
                    )
            }
            .buttonStyle(PlainButtonStyle())

            // 协议文本
            HStack(spacing: 4) {
                Text("subscription.agreement_prompt".localized)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color(hex: "#666666"))

                Button(action: {
                    // TODO: 显示协议内容
                    print("显示会员服务协议")
                }) {
                    Text("subscription.agreement_link".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "#a9cd53"))
                        .underline()
                }
                .buttonStyle(PlainButtonStyle())
            }

            Spacer()
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .animation(
            .easeOut(duration: 0.6)
            .delay(DesignSystem.SubscriptionPage.AnimationPresets.SubscribeButton.delay + 0.2),
            value: contentAppeared
        )
    }

    /**
     * 获取按钮文本
     */
    private func getButtonText() -> String {
        if isLoading {
            return "purchase.processing".localized
        } else if purchaseSuccess {
            return "purchase.success_processing".localized
        } else {
            return "subscription.subscribe_button".localized
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color(hex: "#f8fdf0")
            .ignoresSafeArea()

        MembershipContentView(
            selectedMembershipType: .constant(0),
            selectedPriceType: .constant(0),
            agreementAccepted: .constant(false),
            isLoading: false,
            purchaseSuccess: false,
            errorMessage: nil,
            onSubscribePressed: {
                print("订阅按钮被点击")
            }
        )
        .padding()
    }
}
