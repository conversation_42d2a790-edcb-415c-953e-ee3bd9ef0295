//
//  MembershipTabSegment.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 会员等级切换分段选项卡组件
 * 透明背景设计，与背景图完美重合
 * 支持平滑的切换动画
 */
struct MembershipTabSegment: View {
    
    // MARK: - Properties
    @Binding var selectedTab: Int // 0: 初级会员, 1: 高级会员
    let onTabChanged: (Int) -> Void
    
    // MARK: - Computed Properties
    private var tabTitles: [String] {
        return [
            "subscription.membership.basic".localized,
            "subscription.membership.premium".localized
        ]
    }
    
    // MARK: - State
    @State private var tabAppeared = false
    @State private var buttonPressedStates = [false, false]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 透明背景容器
                RoundedRectangle(cornerRadius: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabCornerRadius)
                    .fill(Color.clear) // 完全透明，让背景图透过
                    .frame(
                        width: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.totalWidth,
                        height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight
                    )
                
                // 分段选项卡按钮
                HStack(spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.spacing) {
                    ForEach(0..<tabTitles.count, id: \.self) { index in
                        createTabButton(title: tabTitles[index], index: index, geometry: geometry)
                    }
                }
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
            }
            .position(
                x: geometry.size.width / 2 + DesignSystem.SubscriptionPage.MembershipTab.offsetX,
                y: geometry.size.height / 2 + DesignSystem.SubscriptionPage.MembershipTab.offsetY
            )
        }
        .opacity(tabAppeared ? 1.0 : 0.0)
        .scaleEffect(
            x: tabAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleStart,
            y: tabAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleStart
        )
        .offset(y: tabAppeared ? 0 : DesignSystem.SubscriptionPage.AnimationPresets.Tab.offsetY)
        .animation(
            .spring(
                response: DesignSystem.SubscriptionPage.MembershipTab.animationSpringResponse,
                dampingFraction: DesignSystem.SubscriptionPage.MembershipTab.animationSpringDamping
            ).delay(DesignSystem.SubscriptionPage.AnimationPresets.Tab.delay),
            value: tabAppeared
        )
        .onAppear {
            withAnimation {
                tabAppeared = true
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建分段选项卡按钮
     * 📌 新增：扩大点击区域，提升用户体验
     */
    private func createTabButton(title: String, index: Int, geometry: GeometryProxy) -> some View {
        let isSelected = selectedTab == index
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.self
        
        // 如果设置了自定义宽度，使用它，否则平均分配
        let buttonWidth = config.tabWidth > 0 ? config.tabWidth : geometry.size.width / CGFloat(tabTitles.count)
        
        // 计算扩展的点击区域尺寸
        let touchWidth = max(buttonWidth + config.touchAreaPadding * 2, config.touchAreaMinWidth)
        let touchHeight = max(config.tabHeight + config.touchAreaPadding * 2, config.touchAreaMinHeight)
        
        return Button(action: {
            handleTabSelection(index: index)
        }) {
            // 可见的按钮内容
            createButtonContent(title: title, isSelected: isSelected, width: buttonWidth)
        }
        .frame(width: touchWidth, height: touchHeight) // 扩大的点击区域
        .contentShape(Rectangle()) // 确保整个区域都可点击
        .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
        .scaleEffect(buttonPressedStates[index] ? config.pressedScale : 1.0)
        .animation(
            .easeInOut(duration: config.animationDuration),
            value: buttonPressedStates[index]
        )
        .animation(
            .spring(
                response: config.animationSpringResponse,
                dampingFraction: config.animationSpringDamping
            ),
            value: isSelected
        )
    }
    
    /**
     * 创建按钮内容
     */
    private func createButtonContent(title: String, isSelected: Bool, width: CGFloat) -> some View {
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.self
        
        return Text(title)
            .font(.system(
                size: config.fontSize,
                weight: DesignSystem.SubscriptionPage.MembershipTab.fontWeight
            ))
            .foregroundColor(
                isSelected ?
                DesignSystem.SubscriptionPage.MembershipTab.activeTextColor :
                DesignSystem.SubscriptionPage.MembershipTab.inactiveTextColor
            )
            .frame(width: width, height: config.tabHeight)
            .background(
                RoundedRectangle(cornerRadius: config.tabCornerRadius)
                    .fill(
                        isSelected ?
                        Color(hex: "#a9cd53").opacity(DesignSystem.SubscriptionPage.MembershipTab.selectedBackgroundOpacity) :
                        Color.white.opacity(DesignSystem.SubscriptionPage.MembershipTab.unselectedBackgroundOpacity)
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: config.tabCornerRadius)
                    .stroke(
                        isSelected ?
                        Color(hex: "#a9cd53") :
                        Color(hex: "#e0e0e0"),
                        lineWidth: isSelected ? 2 : 1
                    )
            )
            .shadow(
                color: isSelected ?
                DesignSystem.SubscriptionPage.MembershipTab.activeShadowColor :
                DesignSystem.SubscriptionPage.MembershipTab.inactiveShadowColor,
                radius: config.shadowRadius,
                x: 0,
                y: config.shadowOffsetY
            )
    }
    
    /**
     * 处理选项卡选择
     */
    private func handleTabSelection(index: Int) {
        // 防止重复选择
        guard selectedTab != index else { return }
        
        // 按下效果
        withAnimation(.easeInOut(duration: 0.1)) {
            buttonPressedStates[index] = true
        }
        
        // 恢复按钮状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.1)) {
                buttonPressedStates[index] = false
            }
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 调用回调
        onTabChanged(index)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color(hex: "#f8fdf0")
            .ignoresSafeArea()
        
        MembershipTabSegment(
            selectedTab: .constant(0),
            onTabChanged: { index in
                print("选中选项卡: \(index)")
            }
        )
        .frame(height: 100)
    }
}
