//
//  ProfileView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI
// 注意：暂时注释掉RevenueCat，因为当前项目可能还没有这个依赖
// import RevenueCat

/**
 * 个人中心主视图
 * 包含个人信息、订阅标签和系统设置三个主要部分
 */
struct ProfileView: View {

    // MARK: - Dependencies
    // 注意：暂时注释掉AuthenticationManager，因为当前项目可能还没有这个管理器
    // @EnvironmentObject var authManager: AuthenticationManager

    // MARK: - State
    @State private var pageAppeared = false
    @State private var showSubscriptionView = false // 控制订阅页面显示
    @State private var showProductIntroduction = false // 控制产品介绍页面显示
    @State private var showFeedbackAlert = false // 控制帮助与反馈弹窗
    @State private var showAboutView = false // 控制关于页面显示

    // 试用期提醒弹窗状态
    @State private var showTrialReminderModal = false // 控制试用期订阅提醒弹窗

    // 添加管理器（暂时注释掉，因为当前项目可能还没有这些管理器）
    // @StateObject private var subscriptionManager = CloudKitSubscriptionManager.shared
    // @StateObject private var accountDeletionManager = AccountDeletionManager.shared
    // @StateObject private var trialManager = TrialManager.shared
    // @StateObject private var revenueCatManager = RevenueCatManager.shared

    // MARK: - Computed Properties
    private var userName: String {
        // 暂时使用固定值，后续可以从AuthenticationManager获取
        return "user_info.parent_nickname".localized
    }

    private var userID: String {
        // 暂时使用固定值，后续可以从AuthenticationManager获取
        return "profile.no_email".localized
    }

    private var membershipLevel: String {
        // 暂时使用固定值，后续可以根据试用状态和订阅状态动态调整
        return "subscription.user_level_regular".localized
    }

    private var expirationDate: String {
        // 暂时使用固定值，后续可以根据试用和订阅状态动态调整
        return "subscription.not_activated".localized
    }

    var body: some View {
        ZStack {
            // 美化背景渐变 - 与其他页面保持一致的风格
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 100, height: 100)
                        .offset(x: -30, y: 20)
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -10)
                }
                Spacer()
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.02))
                        .frame(width: 80, height: 80)
                        .offset(x: 20, y: 30)
                }
            }

            // 主要内容区域 - 自适应布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 个人信息组件 - 紧贴屏幕顶部和两侧，使用屏幕高度百分比
                    UserInfoSection(
                        userName: userName,
                        userID: userID,
                        membershipLevel: membershipLevel,
                        expirationDate: expirationDate
                    )
                    .frame(height: geometry.size.height * 0.25) // 使用屏幕高度的25%
                    .frame(maxWidth: .infinity) // 紧贴屏幕两侧
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 50)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)

                    // 订阅标签组件 - 叠加在个人信息组件底部
                    SubscriptionBannerSection {
                        handleViewPlansPressed()
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    .offset(y: -30) // 叠加效果：向上偏移30pt
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)

                    // 可滚动内容区域
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 顶部空间给订阅标签预留
                            Spacer()
                                .frame(height: 0)

                            // 系统设置组件
                            SystemSettingsSection { settingType in
                                handleSettingItemPressed(settingType)
                            }
                            .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                            .padding(.top, 50) // 为订阅标签留出空间
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? -50 : -10) // 向上移动50个点
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: pageAppeared)

                            // 底部空白区域，确保内容不被导航栏遮挡
                            Spacer()
                                .frame(height: DesignSystem.LiquidTabBar.height + 40)
                        }
                    }
                }
            }
        }
        .onAppear {
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
            // 加载用户数据（为后续功能预留）
            loadUserData()
        }
        // 暂时注释掉订阅页面，因为当前项目可能还没有SubscriptionView
        /*
        .fullScreenCover(isPresented: $showSubscriptionView, onDismiss: {
            // 从订阅页面返回时重新加载用户数据，确保显示最新的订阅状态
            loadUserData()
        }) {
            SubscriptionView()
        }
        */
        // 暂时注释掉产品介绍页面，因为当前项目可能还没有ProductIntroductionView
        /*
        .fullScreenCover(isPresented: $showProductIntroduction) {
            ProductIntroductionView()
        }
        */
        // 暂时注释掉关于页面，因为当前项目可能还没有AboutView
        /*
        .sheet(isPresented: $showAboutView) {
            AboutView()
        }
        */
        .alert("feedback.contact_email.title".localized, isPresented: $showFeedbackAlert) {
            Button("feedback.contact_email.confirm".localized, role: .cancel) {
                // 确定按钮，关闭弹窗
            }
        } message: {
            Text("feedback.contact_email.message".localized)
        }



        // 暂时注释掉删除进度显示，因为当前项目可能还没有AccountDeletionProgressView
        /*
        .overlay(
            showDeleteAccountProgress ?
            AccountDeletionProgressView(
                deletionManager: accountDeletionManager,
                onCancel: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                },
                onComplete: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()

                    // 删除完成后跳转到登录页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        authManager.logout()
                    }
                },
                onError: { error in
                    deletionError = error
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                }
            ) : nil
        )
        */

        // 暂时注释掉试用期订阅提醒弹窗，因为当前项目可能还没有TrialSubscriptionReminderModal
        /*
        .overlay(
            showTrialReminderModal ?
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                // 用户点击"谢谢提醒"后，关闭弹窗并跳转到订阅页面
                print("🔔 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            } : nil
        )
        */

    }


}

// MARK: - Settings Row Component (删除这个重复的组件)
/*
private struct SettingsRow: View {
    let icon: String
    let title: String
    let titleColor: Color
    let action: () -> Void

    init(icon: String, title: String, titleColor: Color = DesignSystem.Colors.profileSettingsTextColor, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.titleColor = titleColor
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .frame(width: 20, height: 20)
                    .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)

                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.Body.fontSize,
                        weight: DesignSystem.Typography.Body.fontWeight
                    ))
                    .foregroundColor(titleColor)

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
*/

// MARK: - ProfileView Extension for Action Handlers
extension ProfileView {

    // MARK: - Action Handlers

    /**
     * 处理查看会员方案按钮点击
     */
    private func handleViewPlansPressed() {
        print("查看会员方案功能")

        // 暂时直接显示订阅页面，后续可以根据试用状态处理
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            showSubscriptionView = true
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理设置项点击
     */
    private func handleSettingItemPressed(_ settingType: SettingType) {
        print("设置项被点击: \(settingType.displayName)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        switch settingType {
        case .iCloudSync:
            // 处理iCloud同步
            // TODO: 实现iCloud同步功能
            print("iCloud同步功能")
        case .productIntroduction:
            // 显示产品介绍页面
            // TODO: 实现产品介绍页面
            print("显示产品介绍页面")
        case .feedback:
            // 显示帮助与反馈弹窗
            showFeedbackAlert = true
            print("显示帮助与反馈弹窗")
        case .about:
            // 显示关于页面
            showAboutView = true
            print("显示关于页面")
        case .clearAllData:
            // 直接清除所有数据，不显示弹窗
            clearAllData()
            print("清除所有数据")
        }
    }

    /**
     * 处理退出登录
     * 可作为替代方案使用，当前在alert回调中直接调用logout方法
     */
    private func handleLogout() {
        print("用户确认退出登录")

        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)

        // 添加过渡动画
        withAnimation(.easeInOut(duration: 0.3)) {
            // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
            // authManager.logout()
            print("退出登录功能暂未实现")
        }
    }

    /**
     * 加载用户数据
     * 在页面出现时调用，用于加载和刷新用户信息
     */
    private func loadUserData() {
        // 此处可以添加额外的用户数据加载逻辑
        // 例如从网络或本地数据库获取最新的用户信息
        print("加载用户数据：\(userName)")

        // 暂时注释掉，因为当前项目可能还没有这些管理器
        // trialManager.refreshTrialStatus()

        // 触发订阅状态检查
        // Task {
        //     try? await Purchases.shared.customerInfo()
        // }
    }

    /**
     * 清除所有数据
     */
    private func clearAllData() {
        print("🗑️ 清除所有数据")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()

        // TODO: 实现清除所有数据的逻辑
        // 这里可以清除Core Data中的所有数据
        // 清除UserDefaults中的设置
        // 清除缓存文件等

        print("清除所有数据功能暂未实现")
    }
}

// MARK: - Preview
#Preview {
    ProfileView()
        // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
        // .environmentObject(AuthenticationManager())
        // .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

// MARK: - Preview
#Preview {
    ProfileView()
}
