//
//  DesignSystem.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 设计系统 - 包含所有UI常量和样式定义
 * 基于转团团应用设计规范实现
 */
struct DesignSystem {
    
    // MARK: - Colors
    struct Colors {
        static let primary = Color(hex: "#B5E36B")
        static let primaryLight = Color(hex: "#E8F9C5")
        static let secondary = Color(hex: "#FFE49E")
        static let textPrimary = Color(hex: "#555555")
        static let textSecondary = Color(hex: "#999999")
        static let textTertiary = Color(hex: "#CCCCCC")
        static let textScore = Color(hex: "#87C441")
        static let errorColor = Color(hex: "#FF5B5B")
        static let textButton = Color.white
        static let background = Color(hex: "#fcfff4")
        static let cardBackground = Color.white
        static let tagSelected = Color(hex: "#FFE49E")
        static let tagUnselected = Color.white
        static let tabBarActive = Color(hex: "#A5C84A")
        static let tabBarInactive = Color(hex: "#CCCCCC")
        static let studentCardBorder = Color(hex: "#F2F2F2")
        
        // 家庭成员详情页专用颜色
        static let memberDetailCardBackground = Color(hex: "#edf6d9")
        static let actionButtonBackground = Color(hex: "#a9d051")
        static let scoreDisplay = Color(hex: "#74c07f")
        static let historyBackground = Color(hex: "#ededed")
        static let textPositive = Color(hex: "#26C34B")
        static let textNegative = Color(hex: "#FF5B5B")
        
        // 个人中心页面专用颜色
        static let profileUserInfoBackground = Color(hex: "#edf6d9")
        static let profileSubscriptionBannerBackground = Color(hex: "#b7da93")
        static let profileSubscriptionButtonBackground = Color(hex: "#5dbb93")
        static let profileSettingsItemBackground = Color.white.opacity(0.9)
        static let profileSettingsIconColor = Color(hex: "#666666")
        static let profileSettingsTextColor = Color(hex: "#333333")
        static let profileSettingsArrowColor = Color(hex: "#CCCCCC")
    }
    
    // MARK: - Typography
    struct Typography {
        static let fontFamily = "PingFang SC"
        
        struct HeadingLarge {
            static let fontSize: CGFloat = 24
            static let fontWeight = Font.Weight.bold
            static let lineHeight: CGFloat = 32
        }
        
        struct HeadingMedium {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.semibold
            static let lineHeight: CGFloat = 24
        }
        
        struct Body {
            static let fontSize: CGFloat = 16
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 22
        }
        
        struct Caption {
            static let fontSize: CGFloat = 14
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 20
        }
        
        struct Score {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.medium
        }
        
        struct MemberId {
            static let fontSize: CGFloat = 36
            static let fontWeight = Font.Weight.heavy
            static let opacity: Double = 0.08
        }
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let pageHorizontal: CGFloat = 25  // 页面水平边距
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }

    // MARK: - Radius (兼容ztt1项目)
    struct Radius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
    }
    
    // MARK: - Shadow
    struct Shadow {
        static let light = Color.black.opacity(0.05)
        static let medium = Color.black.opacity(0.1)
        static let heavy = Color.black.opacity(0.2)
        
        static let radius: CGFloat = 8
        static let offsetY: CGFloat = 2
    }
    
    // MARK: - Animation
    struct Animation {
        static let fast: Double = 0.2
        static let medium: Double = 0.3
        static let slow: Double = 0.5
        static let spring = SwiftUI.Animation.spring(response: 0.6, dampingFraction: 0.8)
    }
    
    // MARK: - Button Styles
    struct Button {
        static let height: CGFloat = 48
        static let cornerRadius: CGFloat = 12
        static let fontSize: CGFloat = 16
        static let fontWeight = Font.Weight.medium
    }
    
    // MARK: - Card Styles
    struct Card {
        static let cornerRadius: CGFloat = 16
        static let padding: CGFloat = 16
        static let shadowRadius: CGFloat = 8
        static let shadowOffset = CGSize(width: 0, height: 2)
        static let shadowOpacity: Double = 0.1
    }
    
    // MARK: - Tab Bar Styles
    struct TabBar {
        static let height: CGFloat = 60
        static let iconSize: CGFloat = 24
        static let fontSize: CGFloat = 12
        static let activeColor = Colors.tabBarActive
        static let inactiveColor = Colors.tabBarInactive
        static let activeBackground = Colors.secondary
    }
    
    // MARK: - Liquid Tab Bar Styles
    struct LiquidTabBar {
        static let height: CGFloat = 100
        static let backgroundColor = Color.white
        static let borderColor = Color(hex: "#a9d051")
        static let borderWidth: CGFloat = 5
        static let bubbleColor = Color(hex: "#FFE49E")
        static let bubbleIconColor = Color(hex: "#666666")
        static let bubbleSize: CGFloat = 80
        static let bubbleIconSize: CGFloat = 35
        static let archHeight: CGFloat = 15
        static let transitionWidth: CGFloat = 40
        static let inactiveIconColor = Color(hex: "#CCCCCC")
        
        // 统一动画配置
        static let animationResponse: Double = 0.6
        static let animationDamping: Double = 0.7
        
        // 图标对称性调整
        static let rightIconOffsetX: CGFloat = -20  // 右侧图标向左偏移量
    }

    // MARK: - Student Card Styles (兼容家庭成员卡片)
    struct StudentCard {
        static let height: CGFloat = 100  // 调整为与参考项目一致
        static let padding: CGFloat = 8
        static let cornerRadius: CGFloat = 16
        static let shadowRadius: CGFloat = 8
        static let shadowOffset = CGSize(width: 0, height: 4)
        static let shadowOpacity: Double = 0.08
    }

    // MARK: - Delete Mode Styles
    struct DeleteMode {
        static let longPressMinimumDuration: Double = 0.8
        static let deleteButtonSize: CGFloat = 24
        static let deleteButtonOffset: CGFloat = 8
    }

    // MARK: - Profile Page Styles
    struct ProfilePage {
        struct UserInfoCard {
            static let height: CGFloat = 120
            static let avatarSize: CGFloat = 48
            static let illustrationSize: CGFloat = 80
            static let cornerRadius: CGFloat = 16
            static let padding: CGFloat = 20
            static let nameFont: CGFloat = 16
            static let membershipFont: CGFloat = 12
            static let idFont: CGFloat = 14
            static let dateFont: CGFloat = 12
        }

        struct SubscriptionBanner {
            static let height: CGFloat = 60
            static let cornerRadius: CGFloat = 12
            static let buttonHeight: CGFloat = 32
            static let buttonCornerRadius: CGFloat = 16
            static let padding: CGFloat = 16
            static let textFont: CGFloat = 14
            static let buttonFont: CGFloat = 12
        }

        struct SettingsItem {
            static let height: CGFloat = 56
            static let cornerRadius: CGFloat = 12
            static let iconSize: CGFloat = 24
            static let textFont: CGFloat = 16
            static let arrowSize: CGFloat = 14
            static let spacing: CGFloat = 12
            static let padding: CGFloat = 16
        }
    }
}
