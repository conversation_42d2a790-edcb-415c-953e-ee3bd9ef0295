//
//  DesignSystem.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 设计系统 - 包含所有UI常量和样式定义
 * 基于转团团应用设计规范实现
 */
struct DesignSystem {
    
    // MARK: - Colors
    struct Colors {
        static let primary = Color(hex: "#B5E36B")
        static let primaryLight = Color(hex: "#E8F9C5")
        static let secondary = Color(hex: "#FFE49E")
        static let textPrimary = Color(hex: "#555555")
        static let textSecondary = Color(hex: "#999999")
        static let textTertiary = Color(hex: "#CCCCCC")
        static let textScore = Color(hex: "#87C441")
        static let errorColor = Color(hex: "#FF5B5B")
        static let textButton = Color.white
        static let background = Color(hex: "#fcfff4")
        static let cardBackground = Color.white
        static let tagSelected = Color(hex: "#FFE49E")
        static let tagUnselected = Color.white
        static let tabBarActive = Color(hex: "#A5C84A")
        static let tabBarInactive = Color(hex: "#CCCCCC")
        static let studentCardBorder = Color(hex: "#F2F2F2")
        
        // 家庭成员详情页专用颜色
        static let memberDetailCardBackground = Color(hex: "#edf6d9")
        static let actionButtonBackground = Color(hex: "#a9d051")
        static let scoreDisplay = Color(hex: "#74c07f")
        static let historyBackground = Color(hex: "#ededed")
        static let textPositive = Color(hex: "#26C34B")
        static let textNegative = Color(hex: "#FF5B5B")
        
        // 个人中心页面专用颜色
        static let profileUserInfoBackground = Color(hex: "#edf6d9")
        static let profileSubscriptionBannerBackground = Color(hex: "#b7da93")
        static let profileSubscriptionButtonBackground = Color(hex: "#5dbb93")
        static let profileSettingsItemBackground = Color.white.opacity(0.9)
        static let profileSettingsIconColor = Color(hex: "#666666")
        static let profileSettingsTextColor = Color(hex: "#333333")
        static let profileSettingsArrowColor = Color(hex: "#CCCCCC")
    }
    
    // MARK: - Typography
    struct Typography {
        static let fontFamily = "PingFang SC"
        
        struct HeadingLarge {
            static let fontSize: CGFloat = 24
            static let fontWeight = Font.Weight.bold
            static let lineHeight: CGFloat = 32
        }
        
        struct HeadingMedium {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.semibold
            static let lineHeight: CGFloat = 24
        }
        
        struct Body {
            static let fontSize: CGFloat = 16
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 22
        }
        
        struct Caption {
            static let fontSize: CGFloat = 14
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 20
        }
        
        struct Score {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.medium
        }
        
        struct MemberId {
            static let fontSize: CGFloat = 36
            static let fontWeight = Font.Weight.heavy
            static let opacity: Double = 0.08
        }
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let pageHorizontal: CGFloat = 25  // 页面水平边距
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }

    // MARK: - Radius (兼容ztt1项目)
    struct Radius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
    }
    
    // MARK: - Shadow
    struct Shadow {
        static let light = Color.black.opacity(0.05)
        static let medium = Color.black.opacity(0.1)
        static let heavy = Color.black.opacity(0.2)
        
        static let radius: CGFloat = 8
        static let offsetY: CGFloat = 2
    }
    
    // MARK: - Animation
    struct Animation {
        static let fast: Double = 0.2
        static let medium: Double = 0.3
        static let slow: Double = 0.5
        static let spring = SwiftUI.Animation.spring(response: 0.6, dampingFraction: 0.8)
    }
    
    // MARK: - Button Styles
    struct Button {
        static let height: CGFloat = 48
        static let cornerRadius: CGFloat = 12
        static let fontSize: CGFloat = 16
        static let fontWeight = Font.Weight.medium
    }
    
    // MARK: - Card Styles
    struct Card {
        static let cornerRadius: CGFloat = 16
        static let padding: CGFloat = 16
        static let shadowRadius: CGFloat = 8
        static let shadowOffset = CGSize(width: 0, height: 2)
        static let shadowOpacity: Double = 0.1
    }
    
    // MARK: - Tab Bar Styles
    struct TabBar {
        static let height: CGFloat = 60
        static let iconSize: CGFloat = 24
        static let fontSize: CGFloat = 12
        static let activeColor = Colors.tabBarActive
        static let inactiveColor = Colors.tabBarInactive
        static let activeBackground = Colors.secondary
    }
    
    // MARK: - Liquid Tab Bar Styles
    struct LiquidTabBar {
        static let height: CGFloat = 100
        static let backgroundColor = Color.white
        static let borderColor = Color(hex: "#a9d051")
        static let borderWidth: CGFloat = 5
        static let bubbleColor = Color(hex: "#FFE49E")
        static let bubbleIconColor = Color(hex: "#666666")
        static let bubbleSize: CGFloat = 80
        static let bubbleIconSize: CGFloat = 35
        static let archHeight: CGFloat = 15
        static let transitionWidth: CGFloat = 40
        static let inactiveIconColor = Color(hex: "#CCCCCC")
        
        // 统一动画配置
        static let animationResponse: Double = 0.6
        static let animationDamping: Double = 0.7
        
        // 图标对称性调整
        static let rightIconOffsetX: CGFloat = -20  // 右侧图标向左偏移量
    }

    // MARK: - Student Card Styles (兼容家庭成员卡片)
    struct StudentCard {
        static let height: CGFloat = 100  // 调整为与参考项目一致
        static let padding: CGFloat = 8
        static let cornerRadius: CGFloat = 16
        static let shadowRadius: CGFloat = 8
        static let shadowOffset = CGSize(width: 0, height: 4)
        static let shadowOpacity: Double = 0.08
    }

    // MARK: - Delete Mode Styles
    struct DeleteMode {
        static let longPressMinimumDuration: Double = 0.8
        static let deleteButtonSize: CGFloat = 24
        static let deleteButtonOffset: CGFloat = 8
    }

    // MARK: - Subscription Page Styles
    struct SubscriptionPage {
        struct UserInfoSection {
            static let heightPercentage: CGFloat = 0.30 // 30%屏幕高度
            static let avatarSize: CGFloat = 48
            static let crownSize: CGFloat = 96 // 变大1.5倍：64 * 1.5 = 96
            static let crownRotation: Double = 30 // 调整为30度
            static let cornerRadius: CGFloat = 30
            static let padding: CGFloat = 20
            static let nameFont: CGFloat = 20
            static let membershipFont: CGFloat = 12
            static let idFont: CGFloat = 14
            static let dateFont: CGFloat = 12

            // MARK: - 独立定位配置
            // 每个元素都有独立的位置参数，可单独调整而不影响其他元素

            // 用户头像独立位置
            static var avatarPositionX: CGFloat = 60  // 头像X坐标
            static var avatarPositionY: CGFloat = 120 // 头像Y坐标

            // 用户信息文本独立位置
            static var userInfoPositionX: CGFloat = 180 // 用户信息X坐标
            static var userInfoPositionY: CGFloat = 120 // 用户信息Y坐标

            // 皇冠图标独立位置
            static var crownPositionX: CGFloat = 320 // 皇冠X坐标
            static var crownPositionY: CGFloat = 120 // 皇冠Y坐标

            // 返回按钮独立位置
            static var backButtonPositionX: CGFloat = 40  // 返回按钮X坐标
            static var backButtonPositionY: CGFloat = 60  // 返回按钮Y坐标
        }

        struct MembershipTab {
            static let heightPercentage: CGFloat = 0.25 // 距离顶部25%
            static let height: CGFloat = 44
            static let cornerRadius: CGFloat = 16
            static let fontSize: CGFloat = 14
            static let fontWeight = Font.Weight.medium
            static let activeTextColor = Color.white
            static let inactiveTextColor = Color(hex: "#333333")
            static let activeShadowColor = Color(hex: "#a9cd53").opacity(0.3)
            static let inactiveShadowColor = Color.black.opacity(0.1)

            // 📌 新增：分段选项卡调整参数 - 让用户可以手动调整

            // 选项卡整体位置调整
            static var offsetX: CGFloat = 0              // 水平偏移 (负值向左，正值向右)
            static var offsetY: CGFloat = 30              // 垂直偏移 (负值向上，正值向下)
            static var topOffsetPercentage: CGFloat = 0.25 // 距离顶部的百分比位置 (0.0-1.0)

            // 选项卡尺寸调整
            static var tabWidth: CGFloat = 120           // 单个选项卡宽度 (0为自动计算)
            static var tabHeight: CGFloat = 44           // 选项卡高度
            static var totalWidth: CGFloat = 280         // 整个选项卡容器宽度
            static var spacing: CGFloat = 8              // 选项卡之间的间距

            // 选项卡外观调整
            static var tabCornerRadius: CGFloat = 16     // 选项卡圆角
            static var selectedBackgroundOpacity: Double = 1.0  // 选中状态背景透明度
            static var unselectedBackgroundOpacity: Double = 0.3 // 未选中状态背景透明度

            // 选项卡阴影调整
            static var shadowRadius: CGFloat = 4         // 阴影半径
            static var shadowOpacity: Double = 0.2       // 阴影透明度
            static var shadowOffsetY: CGFloat = 2        // 阴影Y偏移

            // 选项卡交互调整
            static var pressedScale: CGFloat = 0.95      // 按下时的缩放比例
            static var animationDuration: Double = 0.3   // 动画持续时间
            static var animationSpringResponse: Double = 0.5 // 弹簧动画响应时间
            static var animationSpringDamping: Double = 0.7  // 弹簧动画阻尼

            // 选项卡点击区域扩展 (提升用户体验)
            static var touchAreaPadding: CGFloat = 10    // 点击区域扩展边距
            static var touchAreaMinWidth: CGFloat = 60   // 最小点击区域宽度
            static var touchAreaMinHeight: CGFloat = 44  // 最小点击区域高度
        }

        struct BackgroundImage {
            static let primaryImageName = "初级会员"
            static let advancedImageName = "高级会员"
            static let animationDuration: Double = 0.5

            // 📌 新增：背景图调整参数 - 让用户可以手动调整

            // 背景图整体位置调整
            static var offsetX: CGFloat = 0          // 水平偏移 (负值向左，正值向右)
            static var offsetY: CGFloat = 15          // 垂直偏移 (负值向上，正值向下)

            // 背景图尺寸调整
            static var scaleX: CGFloat = 1.0         // 水平缩放比例 (1.0为原始大小)
            static var scaleY: CGFloat = 1.0         // 垂直缩放比例 (1.0为原始大小)
            static var uniformScale: CGFloat = 1.1   // 统一缩放比例 (会同时影响scaleX和scaleY)

            // 背景图对齐方式
            enum AlignmentMode {
                case fill       // 填充整个区域
                case fit        // 适应区域大小
                case stretch    // 拉伸到指定大小
            }
            static var alignmentMode: AlignmentMode = .fill

            // 背景图裁剪区域 (百分比，0.0-1.0)
            static var cropTop: CGFloat = 0.0        // 顶部裁剪比例
            static var cropBottom: CGFloat = 0.0     // 底部裁剪比例
            static var cropLeft: CGFloat = 0.0       // 左侧裁剪比例
            static var cropRight: CGFloat = 0.0      // 右侧裁剪比例

            // 背景图透明度
            static var opacity: Double = 1.0         // 背景图透明度 (0.0-1.0)

            // 背景图特效
            static var blur: CGFloat = 0.0           // 模糊效果 (0.0无模糊)
            static var brightness: Double = 0.0      // 亮度调整 (-1.0到1.0)
            static var contrast: Double = 1.0        // 对比度调整 (0.0到2.0)

            // 背景图层级控制
            static var zIndex: Double = 1.0          // 层级顺序 (数值越大越在上层)
        }

        struct PriceCard {
            static let width: CGFloat = 160
            static let height: CGFloat = 160
            static let cornerRadius: CGFloat = 16
            static let spacing: CGFloat = 16
            static let selectedBackgroundColor = Color(hex: "#e7f7c4")
            static let selectedBorderColor = Color(hex: "#a9cd53")
            static let unselectedBackgroundColor = Color(hex: "#f1f6e7")
            static let unselectedBorderColor = Color(hex: "#b3c293")
            static let titleColor = Color(hex: "#8b8b8b")
            static let priceColor = Color(hex: "#a9cd53")
            static let titleFont: CGFloat = 26
            static let priceFont: CGFloat = 50
            static let unitFont: CGFloat = 24
            static let selectedBorderWidth: CGFloat = 10  // 选中状态边框宽度
            static let unselectedBorderWidth: CGFloat = 3 // 未选中状态边框宽度
        }

        struct SubscribeButton {
            static let height: CGFloat = 56
            static let cornerRadius: CGFloat = 28
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.semibold
            static let textColor = Color.white
            static let backgroundColor = Color(hex: "#a9cd53")
            static let disabledBackgroundColor = Color(hex: "#cccccc")
            static let shadowColor = Color(hex: "#a9cd53").opacity(0.3)
            static let shadowRadius: CGFloat = 8
            static let shadowOffsetY: CGFloat = 4
        }

        struct FeatureList {
            static let itemSpacing: CGFloat = 12
            static let iconSize: CGFloat = 24
            static let fontSize: CGFloat = 16
            static let fontWeight = Font.Weight.regular
            static let textColor = Color(hex: "#666666")
            static let iconColor = Color(hex: "#a9cd53")
        }

        struct AnimationPresets {
            struct Background {
                static let fadeInDuration: Double = 0.8
                static let fadeInDelay: Double = 0.0
            }

            struct UserInfo {
                static let delay: Double = 0.2
                static let duration: Double = 0.8
                static let offsetY: CGFloat = 50
            }

            struct Content {
                static let delay: Double = 0.4
                static let duration: Double = 0.8
                static let offsetY: CGFloat = 80
                static let staggerInterval: Double = 0.1
            }

            struct Tab {
                static let delay: Double = 0.3
                static let duration: Double = 0.6
                static let scaleStart: CGFloat = 0.9
                static let scaleEnd: CGFloat = 1.0
                static let offsetY: CGFloat = 20
            }

            struct FeatureList {
                static let itemDelay: Double = 0.1
                static let baseDuration: Double = 0.5
                static let offsetX: CGFloat = -20
            }

            struct PriceCards {
                static let delay: Double = 0.5
                static let duration: Double = 0.8
                static let scaleStart: CGFloat = 0.9
                static let switchDuration: Double = 0.4
            }

            struct SubscribeButton {
                static let delay: Double = 0.6
                static let duration: Double = 0.6
                static let scaleStart: CGFloat = 0.8
                static let pressedScale: CGFloat = 0.95
            }
        }
    }
}

// MARK: - 自适应布局扩展
extension DesignSystem {

    /**
     * 动态获取当前设备适配的布局配置
     * 根据设备类型返回相应的布局参数
     */
    struct AdaptiveLayout {

        // MARK: - 订阅页面自适应布局
        struct SubscriptionPage {

            // MARK: - 用户信息区域自适应配置
            struct UserInfoSection {
                static var heightPercentage: CGFloat {
                    return DeviceDetection.isPad ? 0.25 : DesignSystem.SubscriptionPage.UserInfoSection.heightPercentage
                }

                static var topPositionPercentage: CGFloat {
                    return DeviceDetection.isPad ? 0.15 : 0.2 // iPhone默认值
                }

                static var horizontalPadding: CGFloat {
                    return DeviceDetection.isPad ? 80 : 25 // iPhone默认值
                }

                static var titleFontSize: CGFloat {
                    return DeviceDetection.isPad ? 28 : 22 // iPhone默认值
                }
            }

            // MARK: - 内容区域自适应配置
            struct ContentSection {
                static var topOffsetPercentage: CGFloat {
                    return DeviceDetection.isPad ? 0.45 : 0.55 // iPhone默认值
                }

                static var horizontalPadding: CGFloat {
                    return DeviceDetection.isPad ? 100 : 25 // iPhone默认值
                }

                static var verticalSpacing: CGFloat {
                    return DeviceDetection.isPad ? 40 : 30 // iPhone默认值
                }
            }

            // MARK: - 分段选项卡自适应配置
            struct MembershipTab {
                static var topOffsetPercentage: CGFloat {
                    return DeviceDetection.isPad ? 0.22 : DesignSystem.SubscriptionPage.MembershipTab.topOffsetPercentage
                }

                static var tabWidth: CGFloat {
                    return DeviceDetection.isPad ? 160 : DesignSystem.SubscriptionPage.MembershipTab.tabWidth
                }

                static var tabHeight: CGFloat {
                    return DeviceDetection.isPad ? 50 : DesignSystem.SubscriptionPage.MembershipTab.tabHeight
                }

                static var totalWidth: CGFloat {
                    return DeviceDetection.isPad ? 360 : DesignSystem.SubscriptionPage.MembershipTab.totalWidth
                }

                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 12 : DesignSystem.SubscriptionPage.MembershipTab.spacing
                }

                static var tabCornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 20 : DesignSystem.SubscriptionPage.MembershipTab.tabCornerRadius
                }

                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 18 : DesignSystem.SubscriptionPage.MembershipTab.fontSize
                }

                static var shadowRadius: CGFloat {
                    return DeviceDetection.isPad ? 8 : DesignSystem.SubscriptionPage.MembershipTab.shadowRadius
                }

                static var touchAreaPadding: CGFloat {
                    return DeviceDetection.isPad ? 15 : DesignSystem.SubscriptionPage.MembershipTab.touchAreaPadding
                }

                static var touchAreaMinWidth: CGFloat {
                    return DeviceDetection.isPad ? 80 : DesignSystem.SubscriptionPage.MembershipTab.touchAreaMinWidth
                }

                static var touchAreaMinHeight: CGFloat {
                    return DeviceDetection.isPad ? 60 : DesignSystem.SubscriptionPage.MembershipTab.touchAreaMinHeight
                }
            }

            // MARK: - 价格卡片自适应配置
            struct PriceCard {
                static var width: CGFloat {
                    return DeviceDetection.isPad ? 200 : DesignSystem.SubscriptionPage.PriceCard.width
                }

                static var height: CGFloat {
                    return DeviceDetection.isPad ? 200 : DesignSystem.SubscriptionPage.PriceCard.height
                }

                static var cornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 20 : DesignSystem.SubscriptionPage.PriceCard.cornerRadius
                }

                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 24 : DesignSystem.SubscriptionPage.PriceCard.spacing
                }

                static var titleFont: CGFloat {
                    return DeviceDetection.isPad ? 32 : DesignSystem.SubscriptionPage.PriceCard.titleFont
                }

                static var priceFont: CGFloat {
                    return DeviceDetection.isPad ? 60 : DesignSystem.SubscriptionPage.PriceCard.priceFont
                }

                static var unitFont: CGFloat {
                    return DeviceDetection.isPad ? 28 : DesignSystem.SubscriptionPage.PriceCard.unitFont
                }
            }

            // MARK: - 订阅按钮自适应配置
            struct SubscribeButton {
                static var height: CGFloat {
                    return DeviceDetection.isPad ? 64 : DesignSystem.SubscriptionPage.SubscribeButton.height
                }

                static var cornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 32 : DesignSystem.SubscriptionPage.SubscribeButton.cornerRadius
                }

                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 22 : DesignSystem.SubscriptionPage.SubscribeButton.fontSize
                }

                static var maxWidth: CGFloat {
                    return DeviceDetection.isPad ? 400 : .infinity
                }
            }

            // MARK: - 功能列表自适应配置
            struct FeatureList {
                static var itemSpacing: CGFloat {
                    return DeviceDetection.isPad ? 16 : DesignSystem.SubscriptionPage.FeatureList.itemSpacing
                }

                static var iconSize: CGFloat {
                    return DeviceDetection.isPad ? 28 : DesignSystem.SubscriptionPage.FeatureList.iconSize
                }

                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 18 : DesignSystem.SubscriptionPage.FeatureList.fontSize
                }
            }

            // MARK: - 背景图片自适应配置
            struct BackgroundImage {
                static var offsetX: CGFloat {
                    return DeviceDetection.isPad ? 0 : DesignSystem.SubscriptionPage.BackgroundImage.offsetX
                }

                static var offsetY: CGFloat {
                    return DeviceDetection.isPad ? 20 : DesignSystem.SubscriptionPage.BackgroundImage.offsetY
                }

                static var scaleX: CGFloat {
                    return DeviceDetection.isPad ? 1.2 : DesignSystem.SubscriptionPage.BackgroundImage.scaleX
                }

                static var scaleY: CGFloat {
                    return DeviceDetection.isPad ? 1.2 : DesignSystem.SubscriptionPage.BackgroundImage.scaleY
                }

                static var opacity: Double {
                    return DeviceDetection.isPad ? 0.8 : DesignSystem.SubscriptionPage.BackgroundImage.opacity
                }

                static var blur: CGFloat {
                    return DeviceDetection.isPad ? 1.0 : DesignSystem.SubscriptionPage.BackgroundImage.blur
                }

                // 其他属性直接使用原始配置
                static var uniformScale: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.uniformScale
                }

                static var alignmentMode: DesignSystem.SubscriptionPage.BackgroundImage.AlignmentMode {
                    return DesignSystem.SubscriptionPage.BackgroundImage.alignmentMode
                }

                static var animationDuration: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.animationDuration
                }

                static var brightness: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.brightness
                }

                static var contrast: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.contrast
                }

                static var zIndex: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.zIndex
                }

                static var cropTop: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropTop
                }

                static var cropBottom: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropBottom
                }

                static var cropLeft: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropLeft
                }

                static var cropRight: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropRight
                }
            }
        }
    }
}

// MARK: - View Extensions
extension View {

    /**
     * 应用主要按钮样式
     */
    func primaryButtonStyle() -> some View {
        self
            .frame(height: DesignSystem.Button.height)
            .padding(.horizontal, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.primaryLight)
            .cornerRadius(DesignSystem.Button.cornerRadius)
    }
}
