/* 
 * Localizable.strings (English)
 * ztt2
 * 
 * English localization file for ZTT2 app
 */

// MARK: - Common Button Text
"common.button.cancel" = "Cancel";
"common.button.confirm" = "Confirm";
"common.button.delete" = "Delete";
"common.button.save" = "Save";
"common.button.close" = "Close";

// MARK: - Navigation and Tabs
"tab.home" = "Home";
"tab.diary" = "Growth Diary";
"tab.profile" = "Profile";
"tab.current_selection_format" = "Current Selection: %d";

// MARK: - Home (Family Member Management)
"home.title" = "ZTT";
"home.button.add_member" = "Add Member";
"home.button.lottery_config" = "Lottery Config";
"home.button.family_operation" = "Family Operation";
"home.button.family_total_score" = "Family Total Points";
"home.total_score.title" = "Family Total Points";
"home.member.empty.title" = "No Members";
"home.member.empty.description" = "Click \"Add Member\" to start adding members";

// MARK: - Member Management
"member.delete.title" = "Delete Member";
"member.delete.message" = "Are you sure you want to delete member \"%@\"?";
"member.delete.confirm" = "Confirm Delete";
"member.info.unknown_name" = "Unknown";
"member.info.default_number" = "000";
"member.info.family_info" = "My Family";

// MARK: - Growth Diary Page
"growth_diary.title" = "Growth Diary";
"growth_diary.title.input.placeholder" = "Enter diary title";
"growth_diary.input.placeholder" = "Record today's growth moments...";
"growth_diary.button.save" = "Save Diary";
"growth_diary.button.view_history" = "View History";
"growth_diary.date.label" = "Record Time";
"growth_diary.save.success" = "Diary saved successfully";
"growth_diary.save.error" = "Save failed, please try again";
"growth_diary.input.empty" = "Please enter diary content";

// Select Target Feature
"growth_diary.member.label" = "Select Target";
"growth_diary.member.select" = "Select Child";
"growth_diary.member.title" = "Select Record Target";
"growth_diary.member.empty" = "No children added yet\nPlease add family members on the home page first";
"growth_diary.member.picker.title" = "Select Record Target";

// Date Picker
"growth_diary.date.picker.title" = "Select Date";

// History Records
"growth_diary.history.title" = "Select Member";
"growth_diary.history.empty" = "No children added yet\nPlease add family members on the home page first";

// Report Types
"growth_diary.report.analysis" = "Analysis Report";
"growth_diary.report.growth" = "Growth Report";
"growth_diary.report.analysis.title" = "Points Analysis Report";
"growth_diary.report.analysis.description" = "Behavior analysis report based on point records to help understand child's performance trends";
"growth_diary.report.growth.title" = "Growth Diary Report";
"growth_diary.report.growth.description" = "Growth analysis report based on diary content to deeply understand child's inner world";

// MARK: - Profile Page
"profile.title" = "Profile";
"profile.user_info.title" = "User Information";
"profile.subscription.title" = "Subscription Management";
"profile.subscription.current_status" = "Current Status";
"profile.subscription.upgrade" = "Upgrade Membership";
"profile.subscription.benefits" = "Benefits Description";
"profile.settings.title" = "System Settings";
"profile.settings.language" = "Language Switch";
"profile.settings.history" = "Generation History";
"profile.settings.help" = "Help & Feedback";
"profile.settings.about" = "About";
"profile.settings.delete_account" = "Delete Account";
"profile.settings.logout" = "Logout";

// MARK: - Placeholder Page Text
"placeholder.home.title" = "Home";
"placeholder.diary.title" = "Growth Diary";
"placeholder.profile.title" = "Profile";
"placeholder.developing" = "Feature under development...";

// MARK: - Error Messages
"error.network" = "Network connection failed";
"error.unknown" = "Unknown error";
"error.permission_denied" = "Permission denied";

// MARK: - Member Detail Page
"member_detail.title" = "Member Details";
"member_detail.action.add_points" = "Add Points";
"member_detail.action.deduct_points" = "Deduct Points";
"member_detail.action.exchange" = "Exchange";
"member_detail.action.lottery" = "Lottery";
"member_detail.action.analysis" = "Analysis";
"member_detail.history.points" = "Points History";
"member_detail.history.exchange" = "Exchange History";
"member_detail.history.empty.title" = "No Records";
"member_detail.history.empty.description" = "Student's point change records will be displayed here";
"member_detail.info.role_age_format" = "%@ · %d years old";

// MARK: - Date Range Selection
"date_range.this_week" = "This Week";
"date_range.this_month" = "This Month";
"date_range.custom" = "Custom";
"date_range.select_range" = "Select Time Range";
"date_range.start_date" = "Start Date";
"date_range.end_date" = "End Date";
"date_range.invalid_range" = "End date cannot be earlier than start date";
"date_range.total_score_format" = "Family Total Points in %@";

// MARK: - Success Messages
"success.save" = "Save successful";
"success.delete" = "Delete successful";
"success.update" = "Update successful";

// MARK: - Profile Page
"profile.user_info.id_label" = "ID: %@";
"profile.user_info.membership_expires" = "Member Expires: %@";
"profile.subscription.banner_text" = "Upgrade membership to unlock exclusive features";
"profile.subscription.view_plans_button" = "View Plans";

// MARK: - Subscription Page
"subscription.membership.basic" = "Basic Member";
"subscription.membership.premium" = "Premium Member";
"subscription.subscribe_button" = "Subscribe Now";
"subscription.agreement_notice" = "Please read the Membership Service Agreement before subscribing";
"subscription.agreement_prompt" = "Please read before subscribing";
"subscription.agreement_link" = "Membership Service Agreement";
"subscription.user_info.not_member" = "Not a member, unable to unlock all features";
"subscription.not_activated" = "Not activated";

// MARK: - Subscription Membership Levels
"subscription.user_level_regular" = "Regular User";
"subscription.user_level_premium" = "Premium User";
"subscription.not_activated_message" = "Not activated, unable to unlock all features";

// MARK: - Subscription Features
"subscription.feature.manage_classes_two" = "Manage up to 2 classes";
"subscription.feature.unlock_wheel" = "Unlock wheel tool";
"subscription.feature.multi_device_sync" = "Configure more common class rules";
"subscription.feature.basic_functions" = "Student point management, prize redemption and other basic functions";
"subscription.feature.all_basic_benefits" = "All basic member benefits";
"subscription.feature.manage_classes_five" = "Manage up to 5 classes";
"subscription.feature.unlock_box_scratch" = "Unlock mystery box and scratch card tools";
"subscription.feature.ai_reports" = "Unlock AI-generated analysis reports";

// MARK: - Subscription Pricing Types
"subscription.pricing.monthly" = "Monthly";
"subscription.pricing.yearly" = "Yearly";

// MARK: - Purchase Flow
"purchase.agreement.title" = "Purchase Agreement";
"purchase.agreement.message" = "Please read the Membership Service Agreement before purchasing";
"purchase.agreement.reminder" = "Please check to agree to the Membership Service Agreement first";
"purchase.in_progress" = "Purchasing...";
"purchase.processing" = "Processing...";
"purchase.success" = "Subscription successful";
"purchase.success_processing" = "Subscription successful! Redirecting for you...";
"purchase.failed" = "Purchase failed";

// MARK: - User Interface
"user_info.parent_nickname" = "Parent";
"user_info.premium_member" = "Premium Member";
